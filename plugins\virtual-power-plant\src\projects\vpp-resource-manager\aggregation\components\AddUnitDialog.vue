<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="960px"
    :before-close="handleClose"
    class="add-unit-dialog"
    append-to-body
  >
    <div class="dialog-content">
      <!-- 基本信息表单 -->
      <el-form ref="addUnitForm" :model="formData" :rules="formRules">
        <div class="form-row">
          <el-form-item :label="$T('机组名称')" prop="unitName">
            <el-input
              v-model="formData.unitName"
              :placeholder="$T('请输入内容')"
              class="form-input"
            />
          </el-form-item>
          <el-form-item :label="$T('机组类型')" prop="unitType">
            <el-select
              v-model="formData.unitType"
              :placeholder="$T('请选择机组类型')"
              :disabled="isUnitTypeDisabled"
              @change="handleUnitTypeChange"
            >
              <el-option
                v-for="option in unitTypeOptions"
                :key="option.id"
                :label="$T(option.text)"
                :value="option.id"
              />
            </el-select>
          </el-form-item>
        </div>
      </el-form>

      <!-- 资源列表区域 -->
      <div class="resource-section">
        <div class="section-title">{{ resourceListTitle }}</div>

        <!-- 搜索和筛选 -->
        <div class="resource-filter">
          <el-input
            v-model="resourceSearch"
            :placeholder="$T('请输入关键字')"
            prefix-icon="el-icon-search"
            class="search-input"
            @change="handleSearchChange"
          />
          <div class="custom-select bg-BG1">
            <div class="custom-select-label">
              {{ $T("区域") }}
            </div>
            <el-cascader
              v-model="selectedArea"
              :options="areaOptions"
              :props="{
                value: 'code',
                label: 'name',
                checkStrictly: true,
                expandTrigger: 'hover',
                emitPath: false
              }"
              :placeholder="$T('请选择区域')"
              class="custom-select-content flex-auto"
              clearable
              filterable
            />
          </div>
        </div>

        <!-- 资源表格 -->
        <div class="resource-table">
          <el-table
            ref="resourceTable"
            :data="tableData"
            @selection-change="handleSelectionChange"
            height="440"
          >
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column
              type="index"
              :label="$T('序号')"
              width="80"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="resourceId"
              :label="$T('资源ID')"
              min-width="100"
            />
            <el-table-column
              prop="resourceName"
              :label="$T('资源名称')"
              min-width="160"
              show-overflow-tooltip
            />
            <el-table-column
              prop="districtName"
              :label="$T('区域')"
              min-width="160"
              show-overflow-tooltip
            />
            <el-table-column
              prop="capacity"
              :label="$T('报装容量（kVA）')"
              min-width="120"
            />
            <el-table-column
              prop="directControl"
              :label="$T('平台直控')"
              min-width="80"
            >
              <template slot-scope="scope">
                {{ scope.row.directControl ? $T("是") : $T("否") }}
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <div class="resource-pagination">
          <span>
            {{ $T("共") }}
            <span class="count-number">{{ totalResourceCount }}</span>
            {{ $T("个") }}
          </span>
          <el-pagination
            :current-page="currentPage"
            :page-sizes="[10, 20, 30, 40, 50]"
            :page-size="pageSize"
            :total="totalResourceCount"
            layout="sizes, prev, pager, next, jumper"
            @current-change="handlePageChange"
            @size-change="handlePageSizeChange"
          />
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">{{ $T("取消") }}</el-button>
      <el-button type="primary" @click="handleConfirm">
        {{ $T("确定") }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getAvailableResources } from "@/api/resource-aggregation";
import { getGeographicalData } from "@/api/base-config";
import { getGeographicalNameByCode } from "@/utils/geographicalUtils";

export default {
  name: "AddUnitDialog",
  props: {
    showDialog: {
      type: Number,
      default: 0
    },
    mode: {
      type: String,
      default: "add",
      validator: value => ["add", "edit"].includes(value)
    },
    editData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: false,

      // 表单数据
      formData: {
        unitName: "",
        unitType: 1
      },

      // 表单验证规则
      formRules: {
        unitName: [
          {
            required: true,
            message: this.$T("请输入机组名称"),
            trigger: "blur"
          },
          {
            min: 1,
            max: 50,
            message: this.$T("长度在 1 到 50 个字符"),
            trigger: "blur"
          }
        ],
        unitType: [
          {
            required: true,
            message: this.$T("请选择机组类型"),
            trigger: "change"
          }
        ]
      },

      // 资源搜索和筛选
      resourceSearch: "",
      selectedArea: "",
      areaOptions: [], // 区域级联选项

      // 资源列表数据
      tableData: [],
      selectedResources: [], // 当前页面选中的资源（用于与 el-table 交互）
      allSelectedResources: [], // 所有已选中的资源（跨页持久化）
      isRestoringSelection: false, // 标志位：是否正在恢复选择状态
      editResourceIds: [], // 编辑模式下保存的已绑定资源ID
      processedEditResourceIds: new Set(), // 已经处理过的编辑资源ID集合

      // 分页
      currentPage: 1,
      pageSize: 10,
      // 总数
      totalResourceCount: 0,
      // 提示信息
      UNIT_TYPE_MESSAGES: {
        1: "需求响应机组一般以资源所在地同一个地市聚合",
        2: "调峰机组一般以同一个并网节点聚合",
        3: "调频机组只能绑定直控型的资源，一般以同一个并网节点聚合",
        4: "单个机组所有聚合资源须位于同一现货市场出清节点（220千伏及以上电压等级母线）",
        5: '所有负荷类虚拟电厂现货交易机组合并为一个交易机组（"负荷类中长期交易机组"），其所有发电类虚拟电厂现货交易机组合并为另一个交易机组（"发电类中长期交易机组"）'
      }
    };
  },
  computed: {
    // 机组类型选项
    unitTypeOptions() {
      return this.$store.state.enumerations.vpp_unit_type;
    },

    // 弹窗标题
    dialogTitle() {
      return this.mode === "edit" ? this.$T("编辑") : this.$T("新增");
    },

    // 资源列表标题
    resourceListTitle() {
      if (this.formData.unitType) {
        const selectedOption = this.unitTypeOptions.find(
          option => option.id === this.formData.unitType
        );
        const listType = this.$T("资源列表");
        return `${selectedOption.text}${listType}`;
      }
      return "";
    },

    // 机组类型是否禁用
    isUnitTypeDisabled() {
      // 编辑模式下，如果已绑定资源则不能修改类型
      // 使用 || 0 确保 resourceCount 为 undefined 或 null 时当作 0 处理
      const resourceCount = (this.editData && this.editData.resourceCount) || 0;
      return this.mode === "edit" && resourceCount > 0;
    }
  },
  watch: {
    showDialog: {
      async handler() {
        this.dialogVisible = true;
        await this.initDialog();
      }
    },

    // 监听区域筛选变化
    selectedArea() {
      this.currentPage = 1; // 重置到第一页
      this.loadAvailableResources();
    }
  },
  mounted() {
    this.loadDistricts();
  },
  methods: {
    // 初始化弹窗
    async initDialog() {
      if (this.mode === "edit" && this.editData) {
        // 编辑模式：使用传入的数据
        this.formData = {
          unitName: this.editData.unitName || "",
          unitType: this.editData.typeValue || 1
        };
        // 保存编辑模式下的已绑定资源ID
        this.editResourceIds = this.editData.resourceIds || [];
        // 重置已处理的编辑资源ID集合
        this.processedEditResourceIds = new Set();
      } else {
        this.selectedResources = [];
        this.allSelectedResources = [];
        this.editResourceIds = [];
        this.processedEditResourceIds = new Set();
      }

      this.resourceSearch = "";
      // 默认选择全部区域
      this.selectedArea = 0;
      this.currentPage = 1;

      // 清除表单验证
      this.$nextTick(() => {
        if (this.$refs.addUnitForm) {
          this.$refs.addUnitForm.clearValidate();
        }
      });
      this.loadAvailableResources();
    },
    // 显示机组类型提示信息
    showUnitTypeMessage(unitType) {
      const message = this.UNIT_TYPE_MESSAGES[unitType];

      if (message) {
        this.$message({
          message: this.$T(message),
          type: "success",
          showClose: true
        });
      }
    },

    // 加载区域列表
    async loadDistricts() {
      try {
        const response = await getGeographicalData();
        if (response && response.data) {
          // 添加全部选项
          const allOption = {
            name: this.$T("全部"),
            code: 0,
            leaf: true // 设置为叶子节点，不显示展开箭头
          };
          // 转换数据结构以适配级联选择器
          const provinceOptions = response?.data || [];
          this.areaOptions = [allOption, ...provinceOptions];

          // 默认选择全部
          this.selectedArea = 0;
        }
      } catch (error) {
        this.$message.error(this.$T("加载区域列表失败"));
      }
    },

    // 加载可用资源列表
    async loadAvailableResources() {
      if (!this.formData.unitType) {
        this.allResourceData = [];
        return;
      }

      try {
        const params = {
          unitType: this.formData.unitType,
          district: this.selectedArea,
          pageNum: this.currentPage,
          pageSize: this.pageSize
        };

        // 添加搜索关键词参数
        if (this.resourceSearch.trim()) {
          params.searchKeyword = this.resourceSearch.trim();
        }

        // 编辑模式下传入机组ID
        if (this.mode === "edit" && this.editData && this.editData.unitId) {
          params.unitId = this.editData.unitId;
        }

        const response = await getAvailableResources(params);
        if (response && response.data) {
          const resourceList = response.data.records || [];
          this.tableData = resourceList.map(item => ({
            resourceId: item.id,
            resourceName: item.resource_name,
            district: item.district,
            districtName: getGeographicalNameByCode(
              this.areaOptions,
              item.province,
              item.city,
              item.district
            ),
            capacity: item.registered_capacity,
            directControl: item.platform_direct_control
          }));
          // 如果API返回了total，使用API的total，否则使用list的长度
          this.totalResourceCount = response.data?.total || 0;

          // 编辑模式下，将当前页面中已绑定但未处理过的资源添加到全局选择中
          if (this.mode === "edit" && this.editResourceIds.length > 0) {
            // 遍历当前页面的资源数据，找到id在editResourceIds中且未处理过的资源
            this.tableData.forEach(resource => {
              if (
                this.editResourceIds.includes(resource.resourceId) &&
                !this.processedEditResourceIds.has(resource.resourceId)
              ) {
                // 检查是否已经在allSelectedResources中
                const isAlreadySelected = this.allSelectedResources.some(
                  selected => selected.resourceId === resource.resourceId
                );
                if (!isAlreadySelected) {
                  // 将这些资源添加到allSelectedResources中
                  this.allSelectedResources.push(resource);
                }
                // 标记该资源ID已经处理过
                this.processedEditResourceIds.add(resource.resourceId);
              }
            });
          }

          // 恢复当前页面的选择状态
          this.$nextTick(() => {
            this.restoreCurrentPageSelection();
          });
        } else {
          this.tableData = [];
        }
      } catch (error) {
        this.$message.error(this.$T("加载可用资源失败"));
        this.allResourceData = [];
      }
    },
    // 表格选中状态变化
    handleSelectionChange(selection) {
      // 更新当前页面选择状态
      this.selectedResources = selection;

      // 如果正在恢复选择状态，则不更新全局选择
      if (this.isRestoringSelection) {
        return;
      }

      // 获取当前页面所有资源的ID
      const currentPageResourceIds = this.tableData.map(
        item => item.resourceId
      );

      // 从全局选择中移除当前页面的所有资源
      this.allSelectedResources = this.allSelectedResources.filter(
        resource => !currentPageResourceIds.includes(resource.resourceId)
      );

      // 将当前页面新选中的资源添加到全局选择中
      this.allSelectedResources.push(...selection);
    },

    // 恢复当前页面的选择状态
    restoreCurrentPageSelection() {
      if (!this.$refs.resourceTable) return;

      // 设置恢复标志位
      this.isRestoringSelection = true;

      // 清除当前选择状态
      this.$refs.resourceTable.clearSelection();

      // 根据全局选择状态恢复当前页面的选择
      this.tableData.forEach(row => {
        const isSelected = this.allSelectedResources.some(
          selected => selected.resourceId === row.resourceId
        );
        if (isSelected) {
          this.$refs.resourceTable.toggleRowSelection(row, true);
        }
      });

      // 重置恢复标志位
      this.isRestoringSelection = false;
    },

    // 资源分页变化
    handlePageChange(page) {
      // 在页面切换时先设置恢复标志位，防止数据加载过程中的选择变化影响全局状态
      this.isRestoringSelection = true;
      this.currentPage = page;
      this.loadAvailableResources();
    },

    // 页面大小变化
    handlePageSizeChange(size) {
      // 在页面大小变化时也需要设置恢复标志位
      this.isRestoringSelection = true;
      this.pageSize = size;
      this.currentPage = 1; // 重置到第一页
      this.loadAvailableResources();
    },

    // 处理搜索关键字变化
    handleSearchChange() {
      this.currentPage = 1; // 重置到第一页
      this.loadAvailableResources(); // 重新加载资源列表
    },
    // 处理区域选择变化
    handleUnitTypeChange(value) {
      this.currentPage = 1; // 重置到第一页
      if (!this.isUnitTypeDisabled) {
        this.showUnitTypeMessage(value);
      }
      this.loadAvailableResources();
    },

    // 关闭弹窗
    handleClose() {
      this.resetDialogState();
      this.dialogVisible = false;
    },

    // 取消
    handleCancel() {
      this.resetDialogState();
      this.dialogVisible = false;
    },

    // 重置对话框状态
    resetDialogState() {
      this.formData = {
        unitName: "",
        unitType: 1
      };
      this.selectedResources = [];
      this.allSelectedResources = [];
      this.resourceSearch = "";
      this.currentPage = 1;
      this.editResourceIds = [];
      this.processedEditResourceIds = new Set();
    },

    // 确定
    handleConfirm() {
      // 表单验证
      this.$refs.addUnitForm.validate(valid => {
        if (valid) {
          // 提交数据
          const submitData = {
            ...this.formData,
            selectedResources: this.allSelectedResources
          };

          if (this.mode === "edit") {
            this.$emit("update", submitData);
          } else {
            this.$emit("confirm", submitData);
          }

          this.dialogVisible = false;
          this.resetDialogState();
        } else {
          this.$message.warning(this.$T("请完善表单信息"));
          return false;
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.add-unit-dialog {
  // 弹窗整体居中
  :deep(.el-dialog) {
    margin-top: 5vh !important;
  }
  .dialog-content {
    @include padding(J4);

    .form-row {
      display: flex;
      gap: var(--J4);
    }

    .resource-section {
      .section-title {
        @include margin_bottom(J2);
      }

      .resource-filter {
        display: flex;
        align-items: center;
        @include margin_bottom(J3);
        gap: var(--J3);
        .search-input {
          width: 240px;
        }
        .custom-select {
          width: 240px;
          display: inline-flex;
          align-items: center;
          border: 1px solid;
          border-radius: var(--Ra);
          border-color: var(--B1);

          .custom-select-label {
            display: inline-flex;
            align-items: center;
            white-space: nowrap;
            color: var(--ZS);
            padding-left: var(--J2);
          }

          .custom-select-content {
            :deep(.el-input__inner) {
              border: none;
              padding-left: var(--J2);
            }
          }
        }
      }
      .resource-table {
        @include margin_bottom(J3);
      }

      .resource-pagination {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: var(--J3);
        .count-number {
          @include font_color(ZS);
        }
        .page-size-select {
          width: 100px;
        }
      }
    }
  }
}
</style>
